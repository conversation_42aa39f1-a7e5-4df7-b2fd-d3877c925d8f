/*
Theme Name: Nung-TH
*/
@font-face {
    font-family: 'kanitlight';
    src: url('/wp-content/themes/22-hd/Font/kanit-light-webfont.eot');
    src: url('/wp-content/themes/22-hd/Font/kanit-light-webfont.eot?#iefix') format('embedded-opentype'),
         url('/wp-content/themes/22-hd/Font/kanit-light-webfont.woff2') format('woff2'),
         url('/wp-content/themes/22-hd/Font/kanit-light-webfont.woff') format('woff'),
         url('/wp-content/themes/22-hd/Font/kanit-light-webfont.ttf') format('truetype'),
         url('/wp-content/themes/22-hd/Font/kanit-light-webfont.svg#kanitlight') format('svg');
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'kanitregular';
    src: url('/wp-content/themes/22-hd/Font/kanit-regular-webfont.eot');
    src: url('/wp-content/themes/22-hd/Font/kanit-regular-webfont.eot?#iefix') format('embedded-opentype'),
         url('/wp-content/themes/22-hd/Font/kanit-regular-webfont.woff2') format('woff2'),
         url('/wp-content/themes/22-hd/Font/kanit-regular-webfont.woff') format('woff'),
         url('/wp-content/themes/22-hd/Font/kanit-regular-webfont.ttf') format('truetype'),
         url('/wp-content/themes/22-hd/Font/kanit-regular-webfont.svg#kanitregular') format('svg');
    font-weight: 100;
    font-style: normal;
}

@font-face {
    font-family: 'kanitsemibold';
    src: url('/wp-content/themes/22-hd/Font/kanit-semibold-webfont.eot');
    src: url('/wp-content/themes/22-hd/Font/kanit-semibold-webfont.eot?#iefix') format('embedded-opentype'),
         url('/wp-content/themes/22-hd/Font/kanit-semibold-webfont.woff2') format('woff2'),
         url('/wp-content/themes/22-hd/Font/kanit-semibold-webfont.woff') format('woff'),
         url('/wp-content/themes/22-hd/Font/kanit-semibold-webfont.ttf') format('truetype'),
         url('/wp-content/themes/22-hd/Font/kanit-semibold-webfont.svg#kanitsemibold') format('svg');
    font-weight: 100;
    font-style: normal;

}

body {
    background: #0a0a0a;
		margin: 0;
		font-family: 'kanitlight';
}

*,*:before,*:after {
    box-sizing: inherit
}

html {
    box-sizing: border-box;
		line-height: 1.15;
    -webkit-text-size-adjust: 100%;
}

a {
    color: #d4d4d4;
    text-decoration: none;
}

body,button,input,select,optgroup,textarea {
    color: #d4d4d4;
    font-family: 'kanitlight';
    font-size: 1rem;
    line-height: 1.5
}

h1,h2,h3,h4,h5,h6 {
    clear: both
}

.site-header,#page,.carousel.slide,.site-footer {
    width: 1220px
}

.home .site-header,.home #page,.home .site-footer,.home .carousel.slide {
    width: 1400px
}

main {
    display: block;
		margin-top: 5px;
}

@media screen and (min-width: 1200px) {
    .site-header,.site-footer,.site-main,#page,.carousel.slide {
        width:100% !important
    }
}

/*main*/
#main {
    overflow: hidden;
    border-radius: 5px;
    margin-bottom: 30px;
}

#main h2 {
    display: block;
    border-radius: 10px;
    background: #181818;
    padding: 7px 0;
    margin: 0 0 10px;
    text-align: center;
    font-size: 20px;
    color: #fff;
    font-family: 'kanitlight';
}

.grid-movie-index {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-gap: 10px;
    margin-bottom: 15px;
}

.movie_box {
    overflow: hidden;
    position: relative;
    margin-bottom: 15px;
}

.movie_box a {
    background: #0a0a0a;
    text-decoration: none;
		height: 100%;
    display: block;
}

.movie_box p {
    font-size: 14px;
    font-family: 'kanitregular';
    text-align: center;
    color: #fff;
    margin: 10px 5px;
    height: 40px;
}

span.movie-lang {
    font-family: 'kanitregular';
    font-size: 14px;
    line-height: 1;
    color: #999;
    background: #000;
    margin: 0 auto;
    padding: 6px 15px 5px 15px;
    border-radius: 15px;
    text-align: center;
    display: table;
}

.figure-box span.box-movie-star {
    background: url(/wp-content/themes/22-hd/Image/star.png) no-repeat left;
    background-position: 8px;
    background-position-y: 5px;
    position: absolute;
    top: 5px;
    right: 5px;
    color: #f2b000;
    font-size: 14px;
    line-height: 1;
    border-radius: 10px;
    font-family: 'kanitregular';
    background-color: #000000cc;
    padding: 5px 10px 5px 25px;
}

.figure-box span.box-movie-hd {
    position: absolute;
    top: 5px;
    left: 5px;
    border-radius: 10px;
    background: #000000cc;
    padding: 5px 10px;
    font-size: 14px;
    font-family: 'kanitregular';
    line-height: 1;
    color: #f0f;
}

.movie_box:hover span.movie-lang {
    color: #ffc917;
    background: #292627
}

.movie_box a img {
    vertical-align: middle;
    width: 100%;
		object-fit: cover;
}

.movie_box img {
    border-radius: 10px;
		border: 3px solid #4a4a4a;
}

.grid-movie-index .movie_box img {
    height: 286px;
}

.grid-main {
    display: grid;
    grid-template-columns: 234px 1006px 234px;
    grid-gap: 13px;
    margin-top: 20px;
    width: 1500px;
    margin: 0 auto;
}

/*header*/
.container-index {
    width: 1500px;
    margin: 0 auto;
}

.site-branding {
    width: 100%
}

.nds-title {
    margin: 15px 0 20px;
    border-bottom: 2px solid #303030;
    padding-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nds-title h2, .nds-title h3 {
    margin: 0;
    color: #fff;
    font-size: 24px;
    font-family: 'kanitregular';
    line-height: 1;
}

.grid-movie-advise {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    grid-gap: 10px;
    margin-bottom: 15px;
}

.grid-movie-advise img {
    height: 250px;
}

.mp4hd-content p {
    font-size: 14px;
    padding: 20px 40px;
    margin: 45px 0 50px;
    background: #181818;
    border-radius: 10px;
}

section.header-logo img {
    width: 200px;
    height: auto !important;
    object-fit: cover;
    float: right;
	max-height: 86px
}

.header-info {
    width: 1500px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 15% 70% 15%;
    align-items: center;
    padding: 10px 0 25px;
}

#searchform {
    display: flex;
    align-items: center;
    justify-content: center;
}

.site-branding ul {
    margin: 0;
    padding: 0;
    text-align: center;
}

.site-branding h1 {
    text-align: center;
    color: #999;
    font-size: 18px;
    font-family: 'kanitsemibold'
}

.hd-contact a {
    background: #ac4a94;
    padding: 10px 25px;
    font-size: 18px;
    line-height: 1;
    font-family: 'kanitlight';
    color: #fff;
    border-radius: 27px;
    display: inline-flex;
    align-items: center;
    width: 180px;
    height: 50px;
    justify-content: center;
}

input#input-ser {
    padding: 7px 20px;
    border-radius: 17px;
    background-color: #212120 !important;
    color: gray;
    border: 1px;
    margin: 0;
    background: url('/wp-content/themes/22-hd/Image/icon-search.png') no-repeat left;
    background-position: 16px;
    background-position-y: 11px;
    text-indent: 16px;
    width: 75%;
    font-size: 14px;
    outline: 0
}

.main-navigation {
    display: block;
    width: 100%;
    border-top: 1px solid #424040
}

.main-navigation ul {
    display: block;
    list-style: none;
    margin: 0;
    padding-left: 0;
    text-align: center
}

.menu-menu-container {
    width: 100%;
    text-align: center
}

.main-navigation li {
    position: relative;
    display: inline-flex;
    padding: 0 2%
}

.main-navigation a {
    display: block;
    text-decoration: none;
    padding: 20px 0;
    font-family: 'kanitsemibold';
    color: #999;
    position: relative
}

.main-navigation a:before {
    content: "";
    position: absolute;
    height: 5px;
    left: 20%;
    right: 20%;
    bottom: 12px;
    border-radius: 15px;
    background-color: #ffb10a;
    visibility: hidden
}

.main-navigation a:hover,.main-navigation .current-menu-item a {
    color: #fff
}

.main-navigation a:hover:before,.main-navigation .current-menu-item a:before {
    visibility: visible
}

/*sidebar*/
.sidebar-header h2 {
    background: #181818;
    padding: 12.5px 0;
    margin: 0;
    text-align: center;
    font-size: 18px;
    font-family: kanitregular;
    line-height: 1;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-bottom: 1px solid #424040;
}

.custom-menu-widget {
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden;
    background: #181818;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.sidebar ul li {
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 0 !important;
    line-height: 1 !important;
    font-size: 14px !important;
    margin: 0px;
    position: relative;
    list-style-type: none;
}

.sidebar li:hover {
    background: #212120;
}

.sidebar ul li a {
    display: inline-block;
    transition: transform 0.5s ease;
    color: #999 !important;
    font-size: 14px !important;
    float: left !important;
    font-weight: 200;
    padding: 15px 20px;
    font-family: 'kanitregular';
    text-decoration: none;
}

.sidebar ul li:hover a {
    transform: scale(1.12);
    color: #fff !important;
}

.sidebar {
    margin-bottom: 10px;
    overflow: hidden;
}

/*pagination*/
.pagination-x {
    overflow: hidden;
    text-align: center;
    margin: 60px 0
}

span.page-numbers.current {
    background: #ffb10a;
    display: inline-flex;
    padding: 6px 16px;
    margin: 0 5px;
    color: #1c1b1b;
    font-family: 'kanitregular';
    border-radius: 20px
}

a.page-numbers,span.page-numbers.dots {
    background: #212120;
    display: inline-flex;
    padding: 8px 16px 6px 16px;
    margin: 0 5px;
    border-radius: 20px;
    font-family: 'kanitregular';
    color: #fff
}

a.page-numbers:hover {
    color: #ffb10a
}

/*footer*/
.content-footer {
    background: url(/wp-content/themes/22-hd/Image/footer-bg.webp);
    min-height: 342px;
    padding: 40px 0
}

.width-auto {
    width: 1500px;
    margin: 0 auto;
}

.content-footer .width-auto {
    display: grid;
    grid-template-columns: 40% 55%;
    grid-column-gap: 5%
}

.content-footer .content a {
    display: inline-block;
    text-align: center
}

.content-footer p {
    font-size: 14px;
    color: #989799;
	margin:0px
}

.footer-site-logo {
    margin: 0 0 30px
}

.t-footer {
    font-size: 18px;
    font-family: kanitregular;
    color: #fff;
    margin: 10px 0 15px
}

p.title-t {
    font-size: 24px;
    color: #fff;
    border-bottom: 2px solid #303030;
    padding-bottom: 10px;
    margin-bottom: 20px
}

.footer-tag {
    margin-top: -25px
}

.f-tag a {
    font-size: 14px;
    font-family: 'kanitregular';
    color: #999;
    background: #212120;
    border-radius: 5px;
    padding: 7px 15px 5px 15px;
    margin: 0 4px 7px 0;
    display: inline-flex
}

.f-tag a:hover {
    background: linear-gradient(to right, #ffb10a, #db17ff);
    color: #212121
}

footer#colophon nav#site-navigation {
    border-top: 0
}

.site-info {
    display: block;
    overflow: hidden;
    text-align: center
}

.site-info p {
    border-top: 1px solid #2a2936;
    font-size: 14px;
    color: #c7c7c7;
    padding: 10px 0;
    margin: 0
}

.site-info a:hover {
    font-size: 14px;
    color: #ffb10a;
    padding: 10px 0;
    text-decoration: none;
    margin: 0
}

.site-info a {
    color: #ffafff;
		font-weight:bold;
}

/*single*/
.h1-text h1, .h1-text h2, .h1-text h4 {
    display: block;
    border-radius: 10px;
    background: #181818;
    padding: 7px 0;
    margin: 0 0 10px;
    text-align: center;
    font-size: 20px;
    color: #fff;
    font-family: 'kanitregular';
}

#main-movie {
    overflow: hidden;
    border-radius: 5px;
    margin-bottom: 30px;
}

.title-box-movie h1 {
    display: block;
    border-radius: 10px;
    background: #181818;
    padding: 7px 0;
    margin: 0 0 10px;
    text-align: center;
    font-size: 20px;
    color: #fff;
    font-family: 'kanitregular';
}

.informs-movie {
    max-width: 100%;
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: 100%;
    grid-template-columns: 100%;
    row-gap: 25px;
}

.trailer-movie {
    position: relative;
    padding-bottom: 56.23%;
    height: 0;
    overflow: hidden;
    max-width: 100%;
    display: block;
}

.trailer-movie img {
    object-fit: cover;
}

.trailer-movie iframe {
    width: 100%;
    height: 100%;
    float: left;
    display: block;
    position: absolute;
}

.h2-trailer h2 {
    font-size: 16px !important;
    font-family: 'kanitregular'  !important;
    text-align: center  !important;
    margin: -15px 0 -10px  !important;
}

.info-blog .detail-movie {
    background: #212120;
    color: #eee;
    padding: 15px 20px;
    display: flex;
    display: -ms-flexbox;
    justify-content: space-between;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    align-items: center;
    border-radius: 17px
}

.info-blog .detail-movie span {
    font-size: 16px;
    font-family: 'kanitregular'
}

span.movies-lang i,.movie-time i {
    margin-right: 5px
}

.info-blog .detail-movie span.star {
    background: #0d0d0c;
    padding: 3px 10px 0 3px;
    border-radius: 5px;
    color: #f5c518;
    font-size: 16px
}

span.movie-hd {
    background: #0d0d0c;
    color: #f0f;
    padding: 5px 13px;
    border-radius: 17px
}

span.movie-zoom {
    background: #0d0d0c;
    color: #fac346;
    padding: 5px 13px;
    border-radius: 17px
}

span.movie-4k {
    color: #0ff;
    background: #0d0d0c;
    padding: 5px 13px;
    border-radius: 17px
}

.info-blog .detail-movie img {
    margin-bottom: -5px;
    margin-right: 5px;
    object-fit: cover;
	width: 44px;
	height: 22px
}

.blog1 a {
    color: #eee
}

.movie-description .thumb-img img {
    border-radius: 10px;
    object-fit: cover;
	height: 400px
}

.thumb-img {
    height: 400px;
}

.movie-description {
    margin-bottom: 15px;
    display: grid;
    grid-template-columns: 270px 1fr;
    grid-gap: 10px
}

.movie-excerpt {
    padding: 10px 15px;
		position: relative;
    height: 100%;
    overflow: hidden;
}

.movie-excerpt.temp-ep {
    padding: 20px 15px;
    border-radius: 5px;
    background: #212120
}

.movie-excerpt h4 {
    color: #fff;
    font-size: 20px;
    font-family: 'kanitregular';
    margin: 0;
    margin-bottom: 10px
}

.movie-excerpt p {
    color: #d4d4d4;
    font-size: 14px;
    margin: 15px 0;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 12;
    overflow: hidden;
    text-overflow: ellipsis;
		height:255px
}

.movie-excerpt a {
    color: #f7b40d
}

.movie-excerpt a:hover {
    text-decoration: underline
}

.movie-tags a {
    display: inline-flex;
    margin: 0 5px 5px 0;
    padding: 3px 20px;
    background: #212120;
    border-radius: 20px;
    font-size: 14px;
    font-family: 'kanitregular';
    color: #999;
    text-decoration: none !important;
    width: 166px;
    justify-content: center;
}

.movie-tags a:hover {
    color: #212121;
    background: linear-gradient(to right, #d9c107, #db17ff);
}

.movie-tags {
    position: absolute;
    float: left;
    display: block;
    height: 64px;
    bottom: 10px;
}

.playing-movie {
		margin: 15px 0 25px 0;
    position: relative;
}

iframe.res-iframe {
    width: 100%;
    height: 625px;
		display: block;
}

ul.menu {
    padding: 0;
}

ul#menu-genre, ul#menu-category {
    background: #181818;
    margin-top: 0px;
}

.footer-site-logo img {
    width: 200px;
		height : auto !important;
}

.movie_box img:hover {
    border: 3px solid #f2b008;
}

.movie_box img {
    border: 3px solid #4a4a4a;
    transition: border 0.3s ease-in-out;
}

.youtube-container {
    position: relative;
    height: 566px;
    background-color: #000
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    cursor: pointer;
    background-color: red;
    border-radius: 15px;
    width: 80px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 4px solid red
}
